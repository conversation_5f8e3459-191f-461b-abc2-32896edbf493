import 'package:flutter/material.dart';
import '../../models/fishing_spot.dart';
import '../../widgets/fishing_spot_marker.dart';

/// 钓点标记测试页面
/// 
/// 用于测试不同点赞数量下的标记显示效果
class MarkerTestPage extends StatelessWidget {
  const MarkerTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 创建测试钓点数据
    final testSpot = FishingSpot(
      id: 'test_spot',
      name: '测试钓点',
      description: '这是一个测试钓点',
      latitude: 39.9042,
      longitude: 116.4074,
      userId: 'test_user',
      created: DateTime.now(),
      updated: DateTime.now(),
      spotEmoji: '🎣',
      fishEmoji: '🐟',
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('钓点标记测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '钓点标记效果预览',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '背景颜色根据点赞数量从白色渐变到红色：',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            // 展示不同点赞数量的标记
            Expanded(
              child: GridView.count(
                crossAxisCount: 3,
                childAspectRatio: 1.0,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildMarkerDemo(testSpot, 0, '0赞'),
                  _buildMarkerDemo(testSpot, 5, '5赞'),
                  _buildMarkerDemo(testSpot, 10, '10赞'),
                  _buildMarkerDemo(testSpot, 15, '15赞'),
                  _buildMarkerDemo(testSpot, 25, '25赞'),
                  _buildMarkerDemo(testSpot, 35, '35赞'),
                  _buildMarkerDemo(testSpot, 50, '50赞'),
                  _buildMarkerDemo(testSpot, 75, '75赞'),
                  _buildMarkerDemo(testSpot, 100, '100赞'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '新版设计特性：',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text('🎨 立体渐变背景，具有轻微3D效果'),
                  Text('🌈 多彩颜色渐变：灰→蓝→绿→黄→橙→红'),
                  Text('✨ 轻微高光效果，增强立体感'),
                  Text('🔲 简洁边框设计，提升视觉层次'),
                  Text('📍 精确的尖点定位指示'),
                  Text('🎯 无阴影效果，保持视觉一致性'),
                  Text('👆 完整的点击交互支持'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarkerDemo(FishingSpot spot, int likesCount, String label) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        FishingSpotMarker(
          spot: spot,
          likesCount: likesCount,
          size: 60.0,
          onTap: () {
            // 测试点击效果
            debugPrint('点击了 $label 的标记');
          },
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}