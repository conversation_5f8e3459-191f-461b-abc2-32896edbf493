import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:latlong2/latlong.dart';
import '../utils/tianditu_utils.dart';

/// 增强的搜索服务
/// 提供智能搜索、热门搜索、搜索建议等功能
class EnhancedSearchService {
  static const String _cacheKey = 'enhanced_search_cache';
  static const String _historyKey = 'search_history_v2';
  static const String _hotSearchKey = 'hot_search_keywords';
  static const int _maxCacheSize = 100;
  static const int _maxHistorySize = 20;
  static const Duration _cacheExpiry = Duration(days: 7);

  /// 智能搜索
  /// 根据查询内容自动选择最佳搜索策略
  static Future<SearchResponse> smartSearch(
    String query, {
    LatLng? currentLocation,
    int maxResults = 10,
  }) async {
    if (query.trim().isEmpty) {
      return SearchResponse.empty();
    }

    try {
      // 记录搜索行为
      await _recordSearchBehavior(query);

      // 先尝试从缓存获取
      final cachedResults = await _getCachedResults(query);
      if (cachedResults.isNotEmpty) {
        debugPrint('从缓存获取搜索结果: $query');
        return SearchResponse.success(
          results: cachedResults,
          source: SearchSource.cache,
          query: query,
        );
      }

      // 智能选择搜索类型
      final searchType = _determineSearchType(query);
      debugPrint('智能搜索: $query, 类型: $searchType');

      List<SearchResult> results = [];

      // 执行搜索
      switch (searchType) {
        case SmartSearchType.suggestion:
          results = await _performSuggestionSearch(query, currentLocation);
          break;
        case SmartSearchType.poi:
          results = await _performPOISearch(query, currentLocation);
          break;
        case SmartSearchType.address:
          results = await _performAddressSearch(query, currentLocation);
          break;
        case SmartSearchType.nearby:
          results = await _performNearbySearch(query, currentLocation);
          break;
      }

      // 缓存结果
      if (results.isNotEmpty) {
        await _cacheResults(query, results);
      }

      return SearchResponse.success(
        results: results,
        source: SearchSource.api,
        query: query,
        searchType: searchType,
      );
    } catch (e) {
      debugPrint('智能搜索失败: $e');
      return SearchResponse.error(
        message: '搜索失败: $e',
        query: query,
      );
    }
  }

  /// 获取搜索建议
  static Future<List<String>> getSearchSuggestions(String query) async {
    if (query.trim().isEmpty) return [];

    try {
      // 从历史记录中匹配
      final history = await getSearchHistory();
      final suggestions = history
          .where((item) => item.toLowerCase().contains(query.toLowerCase()))
          .take(5)
          .toList();

      // 如果历史记录不够，调用API获取建议
      if (suggestions.length < 3) {
        final apiSuggestions = await _getAPISuggestions(query);
        suggestions.addAll(apiSuggestions);
      }

      return suggestions.take(8).toList();
    } catch (e) {
      debugPrint('获取搜索建议失败: $e');
      return [];
    }
  }

  /// 获取热门搜索关键词
  static Future<List<String>> getHotSearchKeywords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hotSearchJson = prefs.getString(_hotSearchKey);
      
      if (hotSearchJson != null) {
        final List<dynamic> hotSearchList = json.decode(hotSearchJson);
        return hotSearchList.cast<String>();
      }
      
      // 默认热门搜索
      return [
        '钓鱼场',
        '水库',
        '公园',
        '河边',
        '湖泊',
        '海边',
        '码头',
        '渔具店',
      ];
    } catch (e) {
      debugPrint('获取热门搜索失败: $e');
      return [];
    }
  }

  /// 获取搜索历史
  static Future<List<String>> getSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_historyKey);
      
      if (historyJson != null) {
        final List<dynamic> historyList = json.decode(historyJson);
        return historyList.cast<String>();
      }
      
      return [];
    } catch (e) {
      debugPrint('获取搜索历史失败: $e');
      return [];
    }
  }

  /// 添加搜索历史
  static Future<void> addSearchHistory(String query) async {
    if (query.trim().isEmpty) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final history = await getSearchHistory();
      
      // 移除重复项
      history.remove(query);
      // 添加到开头
      history.insert(0, query);
      
      // 限制历史记录数量
      if (history.length > _maxHistorySize) {
        history.removeRange(_maxHistorySize, history.length);
      }
      
      await prefs.setString(_historyKey, json.encode(history));
    } catch (e) {
      debugPrint('添加搜索历史失败: $e');
    }
  }

  /// 清除搜索历史
  static Future<void> clearSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_historyKey);
    } catch (e) {
      debugPrint('清除搜索历史失败: $e');
    }
  }

  /// 智能确定搜索类型
  static SmartSearchType _determineSearchType(String query) {
    // 短查询使用建议搜索
    if (query.length <= 2) {
      return SmartSearchType.suggestion;
    }

    // 包含特定关键词的使用POI搜索
    final poiKeywords = ['店', '场', '馆', '中心', '公园', '广场', '市场'];
    if (poiKeywords.any((keyword) => query.contains(keyword))) {
      return SmartSearchType.poi;
    }

    // 包含地址关键词的使用地址搜索
    final addressKeywords = ['路', '街', '巷', '号', '区', '县', '市', '省'];
    if (addressKeywords.any((keyword) => query.contains(keyword))) {
      return SmartSearchType.address;
    }

    // 包含周边关键词的使用周边搜索
    final nearbyKeywords = ['附近', '周边', '附近的', '周围'];
    if (nearbyKeywords.any((keyword) => query.contains(keyword))) {
      return SmartSearchType.nearby;
    }

    // 默认使用POI搜索
    return SmartSearchType.poi;
  }

  /// 执行建议搜索
  static Future<List<SearchResult>> _performSuggestionSearch(
    String query,
    LatLng? currentLocation,
  ) async {
    return await TianDiTuUtils.searchByName(query, queryType: 4) ?? [];
  }

  /// 执行POI搜索
  static Future<List<SearchResult>> _performPOISearch(
    String query,
    LatLng? currentLocation,
  ) async {
    return await TianDiTuUtils.searchByName(query, queryType: 1) ?? [];
  }

  /// 执行地址搜索
  static Future<List<SearchResult>> _performAddressSearch(
    String query,
    LatLng? currentLocation,
  ) async {
    return await TianDiTuUtils.searchByName(query, queryType: 7) ?? [];
  }

  /// 执行周边搜索
  static Future<List<SearchResult>> _performNearbySearch(
    String query,
    LatLng? currentLocation,
  ) async {
    if (currentLocation == null) {
      return await _performPOISearch(query, currentLocation);
    }

    final centerPoint = '${currentLocation.longitude},${currentLocation.latitude}';
    return await TianDiTuUtils.searchByName(
      query,
      queryType: 3,
      centerPoint: centerPoint,
      radius: 5000,
    ) ?? [];
  }

  /// 获取API建议
  static Future<List<String>> _getAPISuggestions(String query) async {
    try {
      final results = await TianDiTuUtils.searchByName(query, queryType: 4);
      return results?.map((r) => r.name).take(5).toList() ?? [];
    } catch (e) {
      debugPrint('获取API建议失败: $e');
      return [];
    }
  }

  /// 记录搜索行为
  static Future<void> _recordSearchBehavior(String query) async {
    // 这里可以添加搜索行为分析，用于优化搜索体验
    debugPrint('记录搜索行为: $query');
  }

  /// 从缓存获取结果
  static Future<List<SearchResult>> _getCachedResults(String query) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheJson = prefs.getString(_cacheKey);
      
      if (cacheJson == null) return [];
      
      final Map<String, dynamic> cache = json.decode(cacheJson);
      final queryKey = query.toLowerCase().trim();
      
      if (cache.containsKey(queryKey)) {
        final cacheItem = cache[queryKey];
        final timestamp = DateTime.fromMillisecondsSinceEpoch(cacheItem['timestamp']);
        
        if (DateTime.now().difference(timestamp) < _cacheExpiry) {
          final List<dynamic> resultsJson = cacheItem['results'];
          return resultsJson.map((json) => SearchResult.fromJson(json)).toList();
        }
      }
      
      return [];
    } catch (e) {
      debugPrint('获取缓存结果失败: $e');
      return [];
    }
  }

  /// 缓存搜索结果
  static Future<void> _cacheResults(String query, List<SearchResult> results) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheJson = prefs.getString(_cacheKey);
      
      Map<String, dynamic> cache = {};
      if (cacheJson != null) {
        cache = json.decode(cacheJson);
      }
      
      final queryKey = query.toLowerCase().trim();
      
      cache[queryKey] = {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'results': results.map((r) => r.toJson()).toList(),
      };
      
      // 清理缓存
      await _cleanupCache(cache);
      
      await prefs.setString(_cacheKey, json.encode(cache));
    } catch (e) {
      debugPrint('缓存搜索结果失败: $e');
    }
  }

  /// 处理统计结果
  static List<SearchResult> _handleStatisticsResult(Map<String, dynamic> data) {
    try {
      final statistics = data['statistics'];
      if (statistics != null && statistics['priorityCitys'] != null) {
        final List<dynamic> cities = statistics['priorityCitys'];
        return cities.take(5).map((city) {
          final lonlat = city['lonlat'] as String;
          final coords = lonlat.split(',');
          
          return SearchResult(
            name: city['adminName'] ?? '',
            address: city['adminName'] ?? '',
            longitude: double.tryParse(coords[0]) ?? 0.0,
            latitude: double.tryParse(coords[1]) ?? 0.0,
            category: '城市',
            province: null,
            city: city['adminName'],
            area: null,
          );
        }).toList();
      }
      return [];
    } catch (e) {
      debugPrint('处理统计结果失败: $e');
      return [];
    }
  }

  /// 清理缓存
  static Future<void> _cleanupCache(Map<String, dynamic> cache) async {
    final now = DateTime.now();
    final keysToRemove = <String>[];
    
    for (final entry in cache.entries) {
      final timestamp = DateTime.fromMillisecondsSinceEpoch(entry.value['timestamp']);
      if (now.difference(timestamp) >= _cacheExpiry) {
        keysToRemove.add(entry.key);
      }
    }
    
    for (final key in keysToRemove) {
      cache.remove(key);
    }
    
    if (cache.length > _maxCacheSize) {
      final sortedEntries = cache.entries.toList()
        ..sort((a, b) => a.value['timestamp'].compareTo(b.value['timestamp']));
      
      final itemsToRemove = cache.length - _maxCacheSize;
      for (int i = 0; i < itemsToRemove; i++) {
        cache.remove(sortedEntries[i].key);
      }
    }
  }
}

/// 智能搜索类型
enum SmartSearchType {
  suggestion, // 建议搜索
  poi,        // POI搜索
  address,    // 地址搜索
  nearby,     // 周边搜索
}

/// 搜索来源
enum SearchSource {
  cache,  // 缓存
  api,    // API
}

/// 搜索响应
class SearchResponse {
  final List<SearchResult> results;
  final SearchSource? source;
  final String query;
  final SmartSearchType? searchType;
  final bool isSuccess;
  final String? errorMessage;

  SearchResponse._({
    required this.results,
    required this.query,
    required this.isSuccess,
    this.source,
    this.searchType,
    this.errorMessage,
  });

  factory SearchResponse.success({
    required List<SearchResult> results,
    required SearchSource source,
    required String query,
    SmartSearchType? searchType,
  }) {
    return SearchResponse._(
      results: results,
      source: source,
      query: query,
      searchType: searchType,
      isSuccess: true,
    );
  }

  factory SearchResponse.error({
    required String message,
    required String query,
  }) {
    return SearchResponse._(
      results: [],
      query: query,
      isSuccess: false,
      errorMessage: message,
    );
  }

  factory SearchResponse.empty() {
    return SearchResponse._(
      results: [],
      query: '',
      isSuccess: true,
    );
  }
}