import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import '../services/enhanced_search_service.dart';
import '../utils/tianditu_utils.dart';
import 'optimized_search_bar.dart';

/// 专业级地名搜索组件
/// 参考高德地图、百度地图等产品级应用的搜索体验
class ProfessionalSearchBar extends StatefulWidget {
  final Function(LatLng) onLocationSelected;
  final VoidCallback? onSearchStarted;
  final VoidCallback? onSearchEnded;
  final LatLng? currentLocation;
  final String? hintText;
  final bool showRecentSearches;

  const ProfessionalSearchBar({
    super.key,
    required this.onLocationSelected,
    this.onSearchStarted,
    this.onSearchEnded,
    this.currentLocation,
    this.hintText = '搜索地点、地址',
    this.showRecentSearches = true,
  });

  @override
  State<ProfessionalSearchBar> createState() => _ProfessionalSearchBarState();
}

class _ProfessionalSearchBarState extends State<ProfessionalSearchBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  List<SearchResult> _searchResults = [];

  @override
  void initState() {
    super.initState();
    _setupFocusListener();
  }

  void _setupFocusListener() {
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        widget.onSearchStarted?.call();
      } else {
        widget.onSearchEnded?.call();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 执行搜索
  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
      });
      return;
    }

    
    try {
      // 使用增强搜索服务
      final response = await EnhancedSearchService.smartSearch(
        query,
        currentLocation: widget.currentLocation,
        maxResults: 10,
      );
      
      if (mounted) {
        if (response.isSuccess) {
          final sortedResults = _sortResultsByDistance(response.results);
          setState(() {
            _searchResults = sortedResults;
          });
        } else {
          setState(() {
            _searchResults = [];
          });
          debugPrint('搜索失败: ${response.errorMessage}');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _searchResults = [];
        });
      }
    }
  }

  /// 按距离排序搜索结果
  List<SearchResult> _sortResultsByDistance(List<SearchResult> results) {
    if (widget.currentLocation == null || results.isEmpty) {
      return results;
    }

    final resultsWithDistance = results.map((result) {
      final distance = _calculateDistance(
        widget.currentLocation!.latitude,
        widget.currentLocation!.longitude,
        result.latitude,
        result.longitude,
      );
      return _ResultWithDistance(result, distance);
    }).toList();

    resultsWithDistance.sort((a, b) => a.distance.compareTo(b.distance));
    return resultsWithDistance.map((item) => item.result).toList();
  }

  /// 计算距离
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371;
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) *
        math.sin(dLon / 2) * math.sin(dLon / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) => degrees * (math.pi / 180);


  /// 清除搜索内容
  void _clearSearch() {
    _controller.clear();
    setState(() {
      _searchResults = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // [*参数调整*]搜索栏
        Container(
          margin: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top + 0,
            left: 16,
            right: 16,
          ),
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(10),
            shadowColor: Colors.black.withValues(alpha: 0.1),
            child: Container(
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 16,
                  ),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.all(16),
                    child: FaIcon(
                      FontAwesomeIcons.magnifyingGlass,
                      size: 20,
                      color: Colors.grey[400],
                    ),
                  ),
                  suffixIcon: _controller.text.isNotEmpty
                      ? IconButton(
                          icon: const FaIcon(FontAwesomeIcons.xmark, size: 16),
                          onPressed: _clearSearch,
                          color: Colors.grey[400],
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                ),
                style: const TextStyle(fontSize: 16),
                onChanged: (value) {
                  setState(() {});
                  if (value.trim().isNotEmpty) {
                    // 防抖搜索
                    Future.delayed(const Duration(milliseconds: 300), () {
                      if (_controller.text == value && mounted) {
                        _performSearch(value);
                      }
                    });
                  } else {
                    _clearSearch();
                  }
                },
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    _performSearchAndSelectFirst(value);
                  }
                },
              ),
            ),
          ),
        ),
        
        // 搜索结果下拉列表
        if (_searchResults.isNotEmpty)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final result = _searchResults[index];
                return ListTile(
                  dense: true,
                  leading: Icon(
                    FontAwesomeIcons.locationDot,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  title: Text(
                    result.name,
                    style: const TextStyle(fontSize: 14),
                  ),
                  subtitle: Text(
                    result.address,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () => _selectResult(result),
                );
              },
            ),
          ),
      ],
    );
  }

  /// 选择搜索结果
  void _selectResult(SearchResult result) {
    debugPrint('选择搜索结果: ${result.name}, 坐标: (${result.longitude}, ${result.latitude})');
    
    _controller.text = result.name;
    _focusNode.unfocus();
    
    // 清除搜索结果列表
    setState(() {
      _searchResults = [];
    });
    
    // 检查坐标是否有效
    if (result.latitude != 0.0 || result.longitude != 0.0) {
      EnhancedSearchService.addSearchHistory(result.name);
      widget.onLocationSelected(LatLng(result.latitude, result.longitude));
    } else {
      debugPrint('⚠️ 坐标无效，无法移动地图');
    }
  }

  /// 执行搜索并选择第一个结果
  Future<void> _performSearchAndSelectFirst(String query) async {
    await _performSearch(query);
    
    // 如果有搜索结果，自动选择第一个
    if (_searchResults.isNotEmpty) {
      final firstResult = _searchResults.first;
      debugPrint('选择搜索结果: ${firstResult.name}, 坐标: (${firstResult.longitude}, ${firstResult.latitude})');
      
      _controller.text = firstResult.name;
      _focusNode.unfocus();
      
      // 检查坐标是否有效
      if (firstResult.latitude != 0.0 || firstResult.longitude != 0.0) {
        EnhancedSearchService.addSearchHistory(firstResult.name);
        widget.onLocationSelected(LatLng(firstResult.latitude, firstResult.longitude));
      } else {
        debugPrint('⚠️ 坐标无效，无法移动地图');
        // 可以显示错误提示
      }
    } else {
      debugPrint('⚠️ 没有搜索结果');
    }
  }
}

/// 搜索结果与距离的组合类
class _ResultWithDistance {
  final SearchResult result;
  final double distance;
  _ResultWithDistance(this.result, this.distance);
}