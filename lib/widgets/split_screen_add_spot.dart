import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:latlong2/latlong.dart';
import 'package:uuid/uuid.dart';
import 'package:path/path.dart' as path;
import '../models/fishing_spot.dart';
import '../models/emoji_marker.dart';

import '../services/service_locator.dart';
import '../services/image_upload_service.dart';
import 'emoji_marker_picker.dart';
import 'emoji_text_field.dart';
import '../utils/tianditu_utils.dart';

/// 图片上传项目，包含文件和上传状态
class ImageUploadItem {
  final File file;
  double uploadProgress;
  bool isUploading;
  bool isCompleted;
  String? uploadedUrl;
  String? errorMessage;

  ImageUploadItem({
    required this.file,
    this.uploadProgress = 0.0,
    this.isUploading = false,
    this.isCompleted = false,
    this.uploadedUrl,
    this.errorMessage,
  });
}

/// 分屏添加钓点组件
class SplitScreenAddSpot extends StatefulWidget {
  /// 钓点位置
  final LatLng location;

  /// 位置更新回调
  final Function(LatLng) onLocationChanged;

  /// 关闭回调
  final VoidCallback onClose;

  /// 成功添加钓点回调
  final Function(FishingSpot) onSpotAdded;

  /// 建议的钓点名称
  final String? suggestedName;

  const SplitScreenAddSpot({
    super.key,
    required this.location,
    required this.onLocationChanged,
    required this.onClose,
    required this.onSpotAdded,
    this.suggestedName,
  });

  @override
  State<SplitScreenAddSpot> createState() => _SplitScreenAddSpotState();
}

class _SplitScreenAddSpotState extends State<SplitScreenAddSpot> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedSpotType = 'freshwater';
  String _selectedFishType = 'carp';

  String _selectedWaterLevel = '正常';
  final List<Map<String, String>> _waterLevels = [
    {'value': '正常', 'label': '正常', 'emoji': '💧'},
    {'value': '涨水', 'label': '涨水', 'emoji': '🌊'},
    {'value': '退水', 'label': '退水', 'emoji': '🏜️'},
  ];

  String _selectedBait = '商品饵';
  final List<Map<String, String>> _baits = [
    {'value': '商品饵', 'label': '商品饵', 'emoji': '🎣'},
    {'value': '自制饵', 'label': '自制饵', 'emoji': '🥖'},
    {'value': '蚯蚓', 'label': '蚯蚓', 'emoji': '🪱'},
  ];

  final List<ImageUploadItem> _selectedImages = [];
  bool _isLoading = false;
  bool _isRefreshingLocationName = false; // 反查地名加载状态

  // 图片上传服务
  final ImageUploadService _imageUploadService = ImageUploadService();

  @override
  void initState() {
    super.initState();
    // 如果有建议的钓点名称，设置到名称字段中
    if (widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void didUpdateWidget(SplitScreenAddSpot oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当建议名称更新时，如果输入框为空，则填充新的建议名称
    if (widget.suggestedName != oldWidget.suggestedName &&
        widget.suggestedName != null &&
        widget.suggestedName!.trim().isNotEmpty &&
        _nameController.text.trim().isEmpty) {
      _nameController.text = widget.suggestedName!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.5,
      minChildSize: 0.3,
      maxChildSize: 0.85,
      snap: true,
      snapSizes: const [0.3, 0.5, 0.85],
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.white, Colors.grey.shade50],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 32,
                offset: const Offset(0, -8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, -2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 拖拽指示器 - 现代化设计
                          Center(
                            child: Container(
                              margin: const EdgeInsets.only(
                                top: 12,
                                bottom: 16,
                              ),
                              width: 48,
                              height: 5,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.grey.shade300,
                                    Colors.grey.shade400,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(3),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // 标题栏
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 16,
                            ),
                            child: Row(
                              children: [
                                const Text(
                                  '添加钓点',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const Spacer(),
                                IconButton(
                                  icon: const Icon(
                                    Icons.close,
                                    color: Colors.grey,
                                    size: 24,
                                  ),
                                  onPressed: widget.onClose,
                                ),
                              ],
                            ),
                          ),

                          // 坐标信息
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.gps_fixed,
                                  size: 16,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  '坐标位置',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    '${widget.location.latitude.toStringAsFixed(6)}, ${widget.location.longitude.toStringAsFixed(6)}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                    textAlign: TextAlign.right,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // 表单内容区域 - 优化布局
                          Expanded(
                            child: SingleChildScrollView(
                              controller: scrollController,
                              physics: const BouncingScrollPhysics(),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 钓点名称
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          '钓点名称',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: EmojiTextField(
                                                controller: _nameController,
                                                labelText: '钓点名称 *',
                                                validator: (value) {
                                                  if (value == null ||
                                                      value.trim().isEmpty) {
                                                    return '请输入钓点名称';
                                                  }
                                                  return null;
                                                },
                                              ),
                                            ),
                                            const SizedBox(width: 12),
                                            // 反查地名按钮
                                            Container(
                                              decoration: BoxDecoration(
                                                color:
                                                    _isRefreshingLocationName
                                                        ? Colors.orange.shade100
                                                        : Colors.green.shade100,
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                border: Border.all(
                                                  color:
                                                      _isRefreshingLocationName
                                                          ? Colors
                                                              .orange
                                                              .shade300
                                                          : Colors
                                                              .green
                                                              .shade300,
                                                ),
                                              ),
                                              child: IconButton(
                                                onPressed:
                                                    _isRefreshingLocationName
                                                        ? null
                                                        : _refreshLocationName,
                                                icon:
                                                    _isRefreshingLocationName
                                                        ? const SizedBox(
                                                          width: 20,
                                                          height: 20,
                                                          child: CircularProgressIndicator(
                                                            strokeWidth: 2,
                                                            valueColor:
                                                                AlwaysStoppedAnimation<
                                                                  Color
                                                                >(
                                                                  Colors.orange,
                                                                ),
                                                          ),
                                                        )
                                                        : const Icon(
                                                          Icons.refresh_rounded,
                                                          color: Colors.green,
                                                          size: 20,
                                                        ),
                                                padding: const EdgeInsets.all(
                                                  8,
                                                ),
                                                constraints:
                                                    const BoxConstraints(
                                                      minWidth: 40,
                                                      minHeight: 40,
                                                    ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 20),

                                    // 钓点照片
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            const Text(
                                              '钓点照片',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const Spacer(),
                                            Text(
                                              '${_selectedImages.length}/9',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 80,
                                          child: ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            itemCount:
                                                _selectedImages.length + 1,
                                            itemBuilder: (context, index) {
                                              if (index ==
                                                  _selectedImages.length) {
                                                // 添加照片的方框
                                                return Container(
                                                  width: 80,
                                                  height: 80,
                                                  margin: const EdgeInsets.only(
                                                    right: 8,
                                                  ),
                                                  decoration: BoxDecoration(
                                                    border: Border.all(
                                                      color:
                                                          Colors.grey.shade400,
                                                      width: 2,
                                                      style: BorderStyle.solid,
                                                    ),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                    color: Colors.grey.shade100,
                                                  ),
                                                  child: InkWell(
                                                    onTap: _pickImage,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                    child: const Icon(
                                                      Icons.add,
                                                      size: 32,
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                                );
                                              } else {
                                                // 已选择的照片缩略图
                                                return Container(
                                                  width: 80,
                                                  height: 80,
                                                  margin: const EdgeInsets.only(
                                                    right: 8,
                                                  ),
                                                  child: Stack(
                                                    children: [
                                                      ClipRRect(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              8,
                                                            ),
                                                        child: _buildImageThumbnail(
                                                          _selectedImages[index],
                                                        ),
                                                      ),
                                                      // 只在上传完成后显示删除按钮
                                                      if (_selectedImages[index]
                                                          .isCompleted)
                                                        Positioned(
                                                          top: 4,
                                                          right: 4,
                                                          child: GestureDetector(
                                                            onTap:
                                                                () =>
                                                                    _removeImage(
                                                                      index,
                                                                    ),
                                                            child: Container(
                                                              width: 20,
                                                              height: 20,
                                                              decoration:
                                                                  const BoxDecoration(
                                                                    color:
                                                                        Colors
                                                                            .red,
                                                                    shape:
                                                                        BoxShape
                                                                            .circle,
                                                                  ),
                                                              child: const Icon(
                                                                Icons.close,
                                                                color:
                                                                    Colors
                                                                        .white,
                                                                size: 14,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  ),
                                                );
                                              }
                                            },
                                          ),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 20),

                                    // Emoji标记选择器
                                    EmojiMarkerPicker(
                                      selectedSpotType: _selectedSpotType,
                                      selectedFishType: _selectedFishType,
                                      onSelectionChanged: (spotType, fishType) {
                                        setState(() {
                                          _selectedSpotType = spotType;
                                          _selectedFishType = fishType;
                                        });
                                      },
                                    ),

                                    const SizedBox(height: 16),

                                    // 水位选择
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          '水位',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Wrap(
                                          spacing: 8,
                                          runSpacing: 8,
                                          children:
                                              _waterLevels.map((level) {
                                                final isSelected =
                                                    _selectedWaterLevel ==
                                                    level['value'];

                                                return GestureDetector(
                                                  onTap: () {
                                                    setState(() {
                                                      _selectedWaterLevel =
                                                          level['value']!;
                                                    });
                                                  },
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 12,
                                                          vertical: 8,
                                                        ),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          isSelected
                                                              ? Colors.orange
                                                                  .withValues(
                                                                    alpha: 0.15,
                                                                  )
                                                              : Colors.grey
                                                                  .withValues(
                                                                    alpha: 0.08,
                                                                  ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            20,
                                                          ), // 椭圆形
                                                      border:
                                                          isSelected
                                                              ? Border.all(
                                                                color:
                                                                    Colors
                                                                        .orange,
                                                                width: 1.5,
                                                              )
                                                              : Border.all(
                                                                color:
                                                                    Colors
                                                                        .grey
                                                                        .shade300,
                                                                width: 1,
                                                              ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          level['emoji']!,
                                                          style:
                                                              const TextStyle(
                                                                fontSize: 16,
                                                              ),
                                                        ),
                                                        const SizedBox(
                                                          width: 6,
                                                        ),
                                                        Text(
                                                          level['label']!,
                                                          style: TextStyle(
                                                            fontSize: 13,
                                                            color:
                                                                isSelected
                                                                    ? Colors
                                                                        .orange
                                                                        .shade700
                                                                    : Colors
                                                                        .black87,
                                                            fontWeight:
                                                                isSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .normal,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 16),

                                    // 饵料选择
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          '饵料',
                                          style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Wrap(
                                          spacing: 8,
                                          runSpacing: 8,
                                          children:
                                              _baits.map((bait) {
                                                final isSelected =
                                                    _selectedBait ==
                                                    bait['value'];

                                                return GestureDetector(
                                                  onTap: () {
                                                    setState(() {
                                                      _selectedBait =
                                                          bait['value']!;
                                                    });
                                                  },
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 12,
                                                          vertical: 8,
                                                        ),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          isSelected
                                                              ? Colors.purple
                                                                  .withValues(
                                                                    alpha: 0.15,
                                                                  )
                                                              : Colors.grey
                                                                  .withValues(
                                                                    alpha: 0.08,
                                                                  ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                            20,
                                                          ), // 椭圆形
                                                      border:
                                                          isSelected
                                                              ? Border.all(
                                                                color:
                                                                    Colors
                                                                        .purple,
                                                                width: 1.5,
                                                              )
                                                              : Border.all(
                                                                color:
                                                                    Colors
                                                                        .grey
                                                                        .shade300,
                                                                width: 1,
                                                              ),
                                                    ),
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          bait['emoji']!,
                                                          style:
                                                              const TextStyle(
                                                                fontSize: 16,
                                                              ),
                                                        ),
                                                        const SizedBox(
                                                          width: 6,
                                                        ),
                                                        Text(
                                                          bait['label']!,
                                                          style: TextStyle(
                                                            fontSize: 13,
                                                            color:
                                                                isSelected
                                                                    ? Colors
                                                                        .purple
                                                                        .shade700
                                                                    : Colors
                                                                        .black87,
                                                            fontWeight:
                                                                isSelected
                                                                    ? FontWeight
                                                                        .w600
                                                                    : FontWeight
                                                                        .normal,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                        ),
                                      ],
                                    ),

                                    const SizedBox(height: 16),

                                    // 钓点描述
                                    EmojiTextField(
                                      controller: _descriptionController,
                                      labelText: '钓点描述（可选）',
                                      hintText: '描述钓点的特色、环境、注意事项等...',
                                      maxLines: 3,
                                      maxLength: 500,
                                    ),

                                    const SizedBox(height: 32),

                                    // 发布按钮 - 现代化设计
                                    Container(
                                      width: double.infinity,
                                      height: 56,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors:
                                              (_isLoading ||
                                                      _hasUploadingImages)
                                                  ? [
                                                    Colors.grey.shade300,
                                                    Colors.grey.shade400,
                                                  ]
                                                  : _hasUploadingImages
                                                  ? [
                                                    Colors.orange.shade400,
                                                    Colors.orange.shade600,
                                                  ]
                                                  : [
                                                    Colors.green.shade400,
                                                    Colors.green.shade600,
                                                  ],
                                        ),
                                        borderRadius: BorderRadius.circular(16),
                                        boxShadow:
                                            (_isLoading || _hasUploadingImages)
                                                ? []
                                                : [
                                                  BoxShadow(
                                                    color: (_hasUploadingImages
                                                            ? Colors.orange
                                                            : Colors.green)
                                                        .withValues(alpha: 0.4),
                                                    blurRadius: 16,
                                                    offset: const Offset(0, 4),
                                                  ),
                                                  BoxShadow(
                                                    color: Colors.white
                                                        .withValues(alpha: 0.2),
                                                    blurRadius: 8,
                                                    offset: const Offset(0, -2),
                                                  ),
                                                ],
                                      ),
                                      child: ElevatedButton(
                                        onPressed:
                                            (_isLoading || _hasUploadingImages)
                                                ? null
                                                : _handleSubmit,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.transparent,
                                          shadowColor: Colors.transparent,
                                          foregroundColor: Colors.white,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              16,
                                            ),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            if (_hasUploadingImages) ...[
                                              const SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: CircularProgressIndicator(
                                                  strokeWidth: 2.5,
                                                  valueColor:
                                                      AlwaysStoppedAnimation<
                                                        Color
                                                      >(Colors.white),
                                                ),
                                              ),
                                              const SizedBox(width: 12),
                                            ] else if (!(_isLoading ||
                                                _hasUploadingImages)) ...[
                                              Container(
                                                padding: const EdgeInsets.all(
                                                  4,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: Colors.white
                                                      .withValues(alpha: 0.2),
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                ),
                                                child: const Icon(
                                                  Icons.publish_rounded,
                                                  color: Colors.white,
                                                  size: 18,
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                            ],
                                            Text(
                                              _uploadStatusText,
                                              style: const TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                letterSpacing: 0.5,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),

                                    // 底部安全区域
                                    const SizedBox(height: 24),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        );
      },
    );
  }

  // 选择图片 - 直接调用图库
  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> images = await picker.pickMultiImage(
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (images.isNotEmpty) {
        // 创建新的图片项目
        final newItems =
            images
                .map((xFile) => ImageUploadItem(file: File(xFile.path)))
                .toList();

        setState(() {
          // 将选择的图片添加到现有列表中
          _selectedImages.addAll(newItems);
        });

        // 显示选择结果
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已选择 ${images.length} 张图片，开始上传...'),
              duration: const Duration(seconds: 2),
            ),
          );
        }

        // 立即开始上传新选择的图片
        _uploadSelectedImages(newItems);
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('选择图片失败，请重试'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // 上传选中的图片（即时上传）
  Future<void> _uploadSelectedImages(List<ImageUploadItem> items) async {
    // 检查用户是否已登录
    if (!Services.auth.isLoggedIn) {
      // 如果未登录，将图片标记为等待上传状态
      for (var item in items) {
        setState(() {
          item.errorMessage = '请先登录';
        });
      }
      return;
    }

    final user = Services.auth.currentUser;
    if (user == null) return;

    // 立即开始上传每张图片
    for (var item in items) {
      _uploadSingleImage(item, user.id);
    }
  }

  // 上传单张图片
  Future<void> _uploadSingleImage(ImageUploadItem item, String userId) async {
    try {
      // 标记为正在上传
      setState(() {
        item.isUploading = true;
        item.uploadProgress = 0.0;
        item.errorMessage = null;
      });

      // 模拟上传进度（因为实际上传没有进度回调）
      _simulateUploadProgress(item);

      // 使用独立上传服务
      final result = await _imageUploadService.uploadImageIndependent(
        imageFile: item.file,
        userId: userId,
      );

      if (result != null) {
        // 上传成功
        setState(() {
          item.isCompleted = true;
          item.isUploading = false;
          item.uploadProgress = 1.0;
          item.uploadedUrl = result.originalUrl;
        });
        debugPrint('✅ [即时上传] 图片上传成功: ${result.originalUrl}');
      } else {
        // 上传失败
        setState(() {
          item.isUploading = false;
          item.uploadProgress = 0.0;
          item.errorMessage = '上传失败';
        });
        debugPrint('❌ [即时上传] 图片上传失败');
      }
    } catch (e) {
      // 上传异常
      setState(() {
        item.isUploading = false;
        item.uploadProgress = 0.0;
        item.errorMessage = '上传异常: $e';
      });
      debugPrint('❌ [即时上传] 图片上传异常: $e');
    }
  }

  // 模拟上传进度
  void _simulateUploadProgress(ImageUploadItem item) {
    Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (!item.isUploading || item.isCompleted) {
        timer.cancel();
        return;
      }

      setState(() {
        item.uploadProgress += 0.1;
        if (item.uploadProgress >= 0.9) {
          item.uploadProgress = 0.9; // 保持在90%，等待实际上传完成
          timer.cancel();
        }
      });
    });
  }

  // 关联已上传的图片到钓点
  Future<void> _associateUploadedImages(
    FishingSpot spot,
    dynamic user,
    List<ImageUploadItem> uploadedImages,
  ) async {
    debugPrint('🔍 [图片关联] 开始关联已上传的图片');
    debugPrint('🔍 [图片关联] 钓点ID: ${spot.id}');
    debugPrint('🔍 [图片关联] 用户ID: ${user.id}');
    debugPrint('🔍 [图片关联] 图片数量: ${uploadedImages.length}');

    try {
      // 为每张已上传的图片创建数据库记录
      bool saveSuccess = true;
      for (int i = 0; i < uploadedImages.length; i++) {
        final imageItem = uploadedImages[i];
        final fileName = path.basename(imageItem.file.path);

        // TODO: 实现图片记录保存功能
        final success = true; // 临时设为true，需要实现实际的保存逻辑

        if (!success) {
          debugPrint('❌ [图片关联] 照片记录 ${i + 1} 保存失败');
          saveSuccess = false;
        } else {
          debugPrint('✅ [图片关联] 照片记录 ${i + 1} 保存成功');
        }
      }

      if (!saveSuccess) {
        debugPrint('⚠️ [图片关联] 部分照片记录保存失败');
      } else {
        debugPrint('✅ [图片关联] 所有照片记录保存成功');
      }
    } catch (e) {
      debugPrint('❌ [图片关联] 关联失败: $e');
      // 图片关联失败不影响钓点发布，只显示警告
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('钓点发布成功，但图片关联失败: $e')));
      }
    }
  }

  // 检查是否有图片正在上传
  bool get _hasUploadingImages {
    return _selectedImages.any((item) => item.isUploading);
  }

  // 获取上传状态文本
  String get _uploadStatusText {
    final uploadingCount =
        _selectedImages.where((item) => item.isUploading).length;
    if (uploadingCount > 0) {
      return '照片上传中 ($uploadingCount/${_selectedImages.length})';
    }
    return '发布钓点';
  }

  // 构建图片缩略图，支持黑白显示和渐进式彩色
  Widget _buildImageThumbnail(ImageUploadItem item) {
    Widget imageWidget =
        kIsWeb
            ? Image.network(
              item.file.path,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 80,
                  height: 80,
                  color: Colors.grey[300],
                  child: const Icon(Icons.image),
                );
              },
            )
            : Image.file(item.file, width: 80, height: 80, fit: BoxFit.cover);

    // 如果还没开始上传或正在上传，显示黑白图片
    if (!item.isCompleted) {
      imageWidget = ColorFiltered(
        colorFilter: const ColorFilter.matrix([
          0.2126, 0.7152, 0.0722, 0, 0, // Red channel
          0.2126, 0.7152, 0.0722, 0, 0, // Green channel
          0.2126, 0.7152, 0.0722, 0, 0, // Blue channel
          0, 0, 0, 1, 0, // Alpha channel
        ]),
        child: imageWidget,
      );

      // 如果正在上传，显示渐进式彩色效果
      if (item.isUploading && item.uploadProgress > 0) {
        imageWidget = Stack(
          children: [
            imageWidget, // 黑白背景
            ClipRect(
              child: Align(
                alignment: Alignment.centerLeft,
                widthFactor: item.uploadProgress,
                child:
                    kIsWeb
                        ? Image.network(
                          item.file.path,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        )
                        : Image.file(
                          item.file,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
              ),
            ),
          ],
        );
      }
    }

    return imageWidget;
  }

  // 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  // 清空表单内容和状态
  void _clearForm() {
    // 清空文本输入框
    _nameController.clear();
    _descriptionController.clear();

    // 重置选择项为默认值
    setState(() {
      _selectedSpotType = 'freshwater';
      _selectedFishType = 'carp';
      _selectedWaterLevel = '正常';
      _selectedBait = '商品饵';

      // 清空图片列表
      _selectedImages.clear();

      // 重置加载状态
      _isLoading = false;
    });

    debugPrint('✅ [表单清空] 表单内容已清空');
  }

  // 反查地名功能
  Future<void> _refreshLocationName() async {
    setState(() {
      _isRefreshingLocationName = true;
    });

    try {
      debugPrint(
        '开始反查地名: ${widget.location.latitude}, ${widget.location.longitude}',
      );

      final locationName = await TianDiTuUtils.getBestLocationName(
        widget.location.longitude,
        widget.location.latitude,
      );

      if (locationName != null && locationName.trim().isNotEmpty) {
        setState(() {
          _nameController.text = '${locationName.trim()}钓点';
        });
        debugPrint('反查地名成功: ${_nameController.text}');

        // 显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Text('地名获取成功'),
                ],
              ),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        debugPrint('反查地名失败：未获取到有效地名');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  Icon(Icons.warning, color: Colors.white),
                  SizedBox(width: 8),
                  Text('未获取到地名信息'),
                ],
              ),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('反查地名异常: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('获取地名失败: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshingLocationName = false;
        });
      }
    }
  }

  // 处理提交
  Future<void> _handleSubmit() async {
    if (!Services.auth.isLoggedIn) {
      // 跳转到登录页面，等待登录结果
      final result = await Navigator.pushNamed(context, '/login');
      // 如果登录成功，继续发布流程
      if (result == true && Services.auth.isLoggedIn) {
        // 登录成功后，递归调用自己继续发布
        await _handleSubmit();
      }
      return;
    }

    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final user = Services.auth.currentUser;

      // 创建钓点对象
      String description = _descriptionController.text.trim();

      // 将水位和饵料信息添加到描述中
      List<String> additionalInfo = [];
      if (_selectedWaterLevel != '正常') {
        additionalInfo.add('水位：$_selectedWaterLevel');
      }
      if (_selectedBait != '商品饵') {
        additionalInfo.add('饵料：$_selectedBait');
      }

      if (additionalInfo.isNotEmpty) {
        if (description.isNotEmpty) {
          description += '\n\n';
        }
        description += additionalInfo.join('，');
      }

      final clientGeneratedId = const Uuid().v4();
      final spotEmoji = FishingSpotMarkers.getSpotTypeEmoji(_selectedSpotType);
      final fishEmoji = FishingSpotMarkers.getFishTypeEmoji(_selectedFishType);

      debugPrint('🔍 [钓点创建] 钓点类型: $_selectedSpotType, 钓点emoji: $spotEmoji');
      debugPrint('🔍 [钓点创建] 鱼类类型: $_selectedFishType, 鱼类emoji: $fishEmoji');

      final spot = FishingSpot(
        id: clientGeneratedId,
        name: _nameController.text.trim(),
        description: description,
        latitude: widget.location.latitude,
        longitude: widget.location.longitude,
        userId: user?.id ?? '',
        spotType: _selectedSpotType,
        fishTypes: _selectedFishType,
        spotEmoji: spotEmoji,
        fishEmoji: fishEmoji,
        created: DateTime.now(),
        updated: DateTime.now(),
      );

      debugPrint('🔍 [钓点创建] 客户端生成的ID: $clientGeneratedId');
      debugPrint('🔍 [钓点创建] 钓点名称: ${spot.name}');

      // 添加钓点
      final addedSpot = await Services.fishingSpot.addSpot(spot);

      if (addedSpot != null) {
        debugPrint('🔍 [钓点创建] 服务器返回的ID: ${addedSpot.id}');
        debugPrint('🔍 [钓点创建] ID是否相同: ${clientGeneratedId == addedSpot.id}');

        // 如果有已上传的图片，关联到钓点
        final uploadedImages =
            _selectedImages
                .where((item) => item.isCompleted && item.uploadedUrl != null)
                .toList();
        if (uploadedImages.isNotEmpty) {
          debugPrint('🔍 [图片关联] 准备关联 ${uploadedImages.length} 张已上传的图片');
          debugPrint('🔍 [图片关联] 使用的钓点ID: ${addedSpot.id}');
          await _associateUploadedImages(addedSpot, user!, uploadedImages);
        }

        // 添加成功，调用回调
        widget.onSpotAdded(addedSpot);

        if (mounted) {
          // 显示成功提示
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('钓点发布成功！')));

          // 清空表单内容
          _clearForm();

          // 注意：不需要调用 widget.onClose()，因为 onSpotAdded 回调中已经会关闭页面
        }
      } else {
        throw Exception('添加钓点失败');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('发布失败: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
